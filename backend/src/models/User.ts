import mongoose, { Schema, Document, Model } from 'mongoose';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

export interface IUserDocument extends Document {
  fullName: string;
  email: string;
  mobile: string;
  password: string;
  dob: Date;
  role:
    | 'candidate'
    | 'admin'
    | 'verifier'
    | 'course-coordinator'
    | 'super-admin';
  aadhaar: string;
  category: 'state-police' | 'capf' | 'civilian' | 'other';
  isEmailVerified: boolean;
  isMobileVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpire?: Date;
  mobileVerificationOTP?: string;
  mobileVerificationExpire?: Date;
  resetPasswordToken?: string;
  resetPasswordExpire?: Date;
  passwordStrength?: number;
  createdAt: Date;
  matchPassword(enteredPassword: string): Promise<boolean>;
  getEmailVerificationToken(): string;
  getMobileVerificationOTP(): string;
  getResetPasswordToken(): string;
}

interface IUserModel extends Model<IUserDocument> {}

const UserSchema: Schema = new mongoose.Schema({
  fullName: {
    type: String,
    required: [true, 'Please provide your full name'],
    trim: true,
  },
  email: {
    type: String,
    required: [true, 'Please provide an email'],
    unique: true,
    match: [
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Please provide a valid email',
    ],
  },
  mobile: {
    type: String,
    required: [true, 'Please provide a mobile number'],
    match: [/^[0-9]{10}$/, 'Please provide a valid 10-digit mobile number'],
  },
  dob: {
    type: Date,
    required: [true, 'Please provide your date of birth'],
  },
  password: {
    type: String,
    required: [true, 'Please provide a password'],
    minlength: 6,
    select: false,
  },
  passwordStrength: {
    type: Number,
    min: 0,
    max: 100,
  },
  role: {
    type: String,
    enum: [
      'candidate',
      'admin',
      'verifier',
      'course-coordinator',
      'super-admin',
    ],
    default: 'candidate',
  },
  aadhaar: {
    type: String,
    required: [true, 'Please provide your Aadhaar number'],
    match: [/^[0-9]{12}$/, 'Please provide a valid 12-digit Aadhaar number'],
    select: false,
  },
  category: {
    type: String,
    enum: ['state-police', 'capf', 'civilian', 'other'],
    required: [true, 'Please select a category'],
  },
  isEmailVerified: {
    type: Boolean,
    default: false,
  },
  isMobileVerified: {
    type: Boolean,
    default: false,
  },
  emailVerificationToken: String,
  emailVerificationExpire: Date,
  mobileVerificationOTP: String,
  mobileVerificationExpire: Date,
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Encrypt password before saving
UserSchema.pre<IUserDocument>('save', async function (next) {
  if (!this.isModified('password')) {
    next();
    return;
  }

  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

// Match user entered password to hashed password in database
UserSchema.methods.matchPassword = async function (
  enteredPassword: string
): Promise<boolean> {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Generate and hash email verification token
UserSchema.methods.getEmailVerificationToken = function (): string {
  // Generate token
  const verificationToken = crypto.randomBytes(20).toString('hex');

  // Hash token and set to emailVerificationToken field
  this.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');

  // Set expire
  this.emailVerificationExpire = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  return verificationToken;
};

// Generate mobile OTP
UserSchema.methods.getMobileVerificationOTP = function (): string {
  // Generate 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  // Set OTP and expire
  this.mobileVerificationOTP = otp;
  this.mobileVerificationExpire = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  return otp;
};

// Generate and hash password reset token
UserSchema.methods.getResetPasswordToken = function (): string {
  // Generate token
  const resetToken = crypto.randomBytes(20).toString('hex');

  // Hash token and set to resetPasswordToken field
  this.resetPasswordToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  // Set expire
  this.resetPasswordExpire = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  return resetToken;
};

const User = mongoose.model<IUserDocument, IUserModel>('User', UserSchema);

export default User;

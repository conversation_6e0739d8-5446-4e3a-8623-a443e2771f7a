import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  Search,
  Newspaper,
  Award,
  BookOpen,
  Bookmark,
  FileText,
  Users,
} from 'lucide-react';

// Mock news data
const newsItems = [
  {
    id: 1,
    title: 'New Training Program for Cybersecurity Professionals Launched',
    category: 'programs',
    date: '2023-12-15',
    excerpt:
      'Rashtriya Raksha University launches a specialized training program for cybersecurity professionals in collaboration with leading tech companies.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=Cybersecurity+Program',
  },
  {
    id: 2,
    title: 'MoU Signed with Gujarat Police Academy',
    category: 'partnerships',
    date: '2023-11-28',
    excerpt:
      'Rashtriya Raksha University signs Memorandum of Understanding with Gujarat Police Academy to enhance training capabilities.',
    image: 'https://placehold.co/600x400/gyaan-navy/white?text=MoU+Signing',
  },
  {
    id: 3,
    title: 'National Conference on Internal Security Challenges',
    category: 'events',
    date: '2023-10-10',
    excerpt:
      'RRU to host a national conference on emerging internal security challenges and strategies for mitigation.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=National+Conference',
  },
  {
    id: 4,
    title: 'Admission Open for 2024 Academic Year',
    category: 'admissions',
    date: '2023-12-01',
    excerpt:
      'Applications are now being accepted for various undergraduate and postgraduate programs for the 2024 academic year.',
    image: 'https://placehold.co/600x400/gyaan-navy/white?text=Admissions+Open',
  },
  {
    id: 5,
    title: 'Workshop on Forensic Investigation Techniques',
    category: 'events',
    date: '2023-11-15',
    excerpt:
      'A three-day workshop on advanced forensic investigation techniques to be conducted by international experts.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=Forensic+Workshop',
  },
  {
    id: 6,
    title: 'Research Grant Awarded for Border Security Study',
    category: 'research',
    date: '2023-10-25',
    excerpt:
      'RRU researchers receive prestigious grant for comprehensive study on technological solutions for border security.',
    image: 'https://placehold.co/600x400/gyaan-navy/white?text=Research+Grant',
  },
];

const News = () => {
  const [activeCategory, setActiveCategory] = useState('all');

  const filteredNews =
    activeCategory === 'all'
      ? newsItems
      : newsItems.filter((item) => item.category === activeCategory);

  // Get category icon
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'programs':
        return <BookOpen className="h-4 w-4 text-white" />;
      case 'partnerships':
        return <Users className="h-4 w-4 text-white" />;
      case 'events':
        return <Calendar className="h-4 w-4 text-white" />;
      case 'admissions':
        return <FileText className="h-4 w-4 text-white" />;
      case 'research':
        return <Award className="h-4 w-4 text-white" />;
      default:
        return <Newspaper className="h-4 w-4 text-white" />;
    }
  };

  // Get category color
  const getCategoryColor = (category) => {
    switch (category) {
      case 'programs':
        return 'bg-gyaan-navy text-white';
      case 'partnerships':
        return 'bg-gyaan-navy text-white';
      case 'events':
        return 'bg-gyaan-navy text-white';
      case 'admissions':
        return 'bg-gyaan-navy text-white';
      case 'research':
        return 'bg-gyaan-navy text-white';
      default:
        return 'bg-gyaan-navy text-white';
    }
  };

  // Add scroll animation effect
  useEffect(() => {
    const handleScroll = () => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach((element) => {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight - 100;
        if (isVisible) {
          element.classList.add('animate-visible');
        }
      });
    };

    // Initial check
    handleScroll();

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Clean up
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <RRUHeader />
      <Header />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gyaan-navy mb-6">
                News & Announcements
              </h1>
              <div className="w-24 h-1 bg-gyaan-gold mx-auto mb-6"></div>
              <p className="text-xl text-gray-600">
                Stay updated with the latest news, events, and announcements
                from GyaanRaksha Samyog and Rashtriya Raksha University.
              </p>
            </div>
          </div>
        </section>

        {/* News Content */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              {/* Category Heading */}
              <div className="mb-8">
                <h2 className="text-2xl md:text-3xl font-bold text-gyaan-navy text-center mb-8">
                  Browse by Category
                </h2>
              </div>

              {/* Category Tabs */}
              <div className="bg-white p-6 rounded-lg shadow-lg mb-12 border border-gray-200">
                <Tabs defaultValue="all" className="w-full">
                  <TabsList className="grid grid-cols-3 md:grid-cols-6 w-full bg-gray-100 p-1">
                    <TabsTrigger
                      value="all"
                      onClick={() => setActiveCategory('all')}
                      className="data-[state=active]:bg-gyaan-navy data-[state=active]:text-white"
                    >
                      <Newspaper className="h-4 w-4 mr-2" />
                      All
                    </TabsTrigger>
                    <TabsTrigger
                      value="programs"
                      onClick={() => setActiveCategory('programs')}
                      className="data-[state=active]:bg-gyaan-navy data-[state=active]:text-white"
                    >
                      <BookOpen className="h-4 w-4 mr-2" />
                      Programs
                    </TabsTrigger>
                    <TabsTrigger
                      value="partnerships"
                      onClick={() => setActiveCategory('partnerships')}
                      className="data-[state=active]:bg-gyaan-navy data-[state=active]:text-white"
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Partnerships
                    </TabsTrigger>
                    <TabsTrigger
                      value="events"
                      onClick={() => setActiveCategory('events')}
                      className="data-[state=active]:bg-gyaan-navy data-[state=active]:text-white"
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      Events
                    </TabsTrigger>
                    <TabsTrigger
                      value="admissions"
                      onClick={() => setActiveCategory('admissions')}
                      className="data-[state=active]:bg-gyaan-navy data-[state=active]:text-white"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Admissions
                    </TabsTrigger>
                    <TabsTrigger
                      value="research"
                      onClick={() => setActiveCategory('research')}
                      className="data-[state=active]:bg-gyaan-navy data-[state=active]:text-white"
                    >
                      <Award className="h-4 w-4 mr-2" />
                      Research
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              {/* News Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredNews.map((item, index) => {
                  return (
                    <div
                      key={item.id}
                      className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 border border-gray-200"
                    >
                      <div className="relative h-48 overflow-hidden">
                        {/* Category badge */}
                        <div className="absolute top-3 right-3 z-10">
                          <Badge
                            className={`${getCategoryColor(
                              item.category
                            )} px-3 py-1 flex items-center gap-1.5`}
                          >
                            {getCategoryIcon(item.category)}
                            <span className="capitalize">{item.category}</span>
                          </Badge>
                        </div>

                        <img
                          src={item.image}
                          alt={item.title}
                          className="w-full h-full object-cover"
                        />

                        {/* Date on image */}
                        <div className="absolute bottom-3 left-3 z-10 flex items-center text-sm text-white bg-gyaan-navy/80 px-3 py-1 rounded-full">
                          <Calendar size={14} className="mr-2 text-white" />
                          <span>
                            {new Date(item.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                            })}
                          </span>
                        </div>
                      </div>

                      <div className="p-6">
                        <h3 className="text-lg font-bold text-gyaan-navy mb-3 line-clamp-2">
                          {item.title}
                        </h3>
                        <div className="bg-gyaan-gold h-0.5 w-16 mb-4"></div>
                        <p className="text-gray-600 mb-6 line-clamp-3">
                          {item.excerpt}
                        </p>
                        <NavLink to={`/news/${item.id}`} className="block">
                          <Button className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white">
                            Read More
                          </Button>
                        </NavLink>
                      </div>
                    </div>
                  );
                })}
              </div>

              {filteredNews.length === 0 && (
                <div className="bg-white p-8 rounded-lg shadow-lg text-center border border-gray-200">
                  <div className="flex flex-col items-center justify-center">
                    <Search className="h-16 w-16 text-gray-400 mb-4" />
                    <h3 className="text-lg font-bold text-gyaan-navy mb-2">
                      No Results Found
                    </h3>
                    <p className="text-gray-600 mb-6 max-w-md mx-auto">
                      We couldn't find any news items in the "{activeCategory}"
                      category. Please try another category or check back later.
                    </p>
                    <Button
                      onClick={() => setActiveCategory('all')}
                      className="bg-gyaan-navy hover:bg-gyaan-navy/90 text-white"
                    >
                      View All News
                    </Button>
                  </div>
                </div>
              )}

              {/* View All News Button - only show if there are news items and not already showing all */}
              {filteredNews.length > 0 && activeCategory !== 'all' && (
                <div className="text-center mt-12">
                  <Button
                    onClick={() => setActiveCategory('all')}
                    className="bg-gyaan-navy hover:bg-gyaan-navy/90 text-white px-8 py-3 text-lg"
                  >
                    View All News
                  </Button>
                </div>
              )}

              {/* Subscribe to News Section */}
              <div className="mt-16 bg-gray-50 rounded-lg p-8 text-center">
                <h3 className="text-2xl font-bold text-gyaan-navy mb-4">
                  Stay Updated with Latest News
                </h3>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Subscribe to our newsletter to receive the latest news,
                  announcements, and updates from Rashtriya Raksha University
                  directly in your inbox.
                </p>
                <Button className="bg-gyaan-navy hover:bg-gyaan-navy/90 text-white px-8 py-3 text-lg">
                  Subscribe to Newsletter
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default News;
